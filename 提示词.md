请修改 `webgl/touch.html` 页面的 main-content 区域，具体要求如下：

1. **在 electrical-status 区域添加设备控制按钮**：
   - 添加三个控制按钮：启动、停止、复位
   - 按钮样式
   - 按钮功能逻辑参考 `debug1/设备操作.html` 的实现
   - 使用已提取的通用函数：`startOperation()`、`stopOperation()`、`resetOperation()`
   - 添加对应的状态指示器，使用 `.status-indicator` 样式类

2. **同步网侧负载无功电流图表**：
   - 将 `<!-- 中部：网侧负载无功电流图表 -->` 区域的内容与 `main.html` 中的对应区域保持完全一致
   - 不添加任何额外功能，确保图表显示和数据处理逻辑相同
   - 保持ECharts配置和样式的一致性

3. **代码复用和整合**：
   - 确保 `touch.html` 正确引入公共文件：
     ```html
     <link rel="stylesheet" href="common/parameter-config.css">
     <script src="common/parameter-config.js"></script>
     ```
   - 在页面初始化时调用 `initDeviceOperationManager()` 来初始化设备操作功能
   - 如果需要新的MQTT通信逻辑，将其添加到 `webgl/common/parameter-config.js` 中以便其他页面复用
   - 移除 `touch.html` 中重复的内联CSS和JavaScript代码，改为使用公共文件中的功能

4. **保持页面一致性**：
   - 确保新添加的控制按钮与页面整体的工业监控界面风格保持一致
   - 维持响应式设计，适配大屏显示
   - 保持与其他页面相同的MQTT连接状态指示器样式和行为

**预期结果**：
- `touch.html` 页面具备完整的设备控制功能
- 网侧负载无功电流图表与 `main.html` 完全同步
- 代码复用率提高，维护性增强
- 所有功能正常工作，无破坏性变更